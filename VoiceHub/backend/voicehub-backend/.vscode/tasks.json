{"version": "2.0.0", "tasks": [{"label": "maven: compile", "type": "shell", "command": "mvn", "args": ["clean", "compile"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": "$maven-compiler-java", "options": {"env": {"JAVA_HOME": "/Users/<USER>/Library/Java/JavaVirtualMachines/ms-17.0.15/Contents/Home"}}}, {"label": "maven: package", "type": "shell", "command": "mvn", "args": ["clean", "package"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": "$maven-compiler-java", "options": {"env": {"JAVA_HOME": "/Users/<USER>/Library/Java/JavaVirtualMachines/ms-17.0.15/Contents/Home"}}}, {"label": "maven: test", "type": "shell", "command": "mvn", "args": ["test"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": "$maven-compiler-java", "options": {"env": {"JAVA_HOME": "/Users/<USER>/Library/Java/JavaVirtualMachines/ms-17.0.15/Contents/Home"}}}, {"label": "maven: spring-boot:run", "type": "shell", "command": "mvn", "args": ["spring-boot:run"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": "$maven-compiler-java", "options": {"env": {"JAVA_HOME": "/Users/<USER>/Library/Java/JavaVirtualMachines/ms-17.0.15/Contents/Home"}}}]}