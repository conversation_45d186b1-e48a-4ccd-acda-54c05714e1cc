{"version": "0.2.0", "configurations": [{"type": "java", "name": "Launch VoiceHubBackendApplication", "request": "launch", "mainClass": "com.voicehub.backend.VoiceHubBackendApplication", "projectName": "voicehub-backend", "args": "", "vmArgs": "", "env": {"SPRING_PROFILES_ACTIVE": "dev"}, "console": "internalConsole", "stopOnEntry": false, "cwd": "${workspaceFolder}", "preLaunchTask": "maven: compile"}, {"type": "java", "name": "Debug VoiceHubBackendApplication", "request": "launch", "mainClass": "com.voicehub.backend.VoiceHubBackendApplication", "projectName": "voicehub-backend", "args": "", "vmArgs": "-Dspring.profiles.active=dev -Ddebug=true", "env": {"SPRING_PROFILES_ACTIVE": "dev"}, "console": "internalConsole", "stopOnEntry": false, "cwd": "${workspaceFolder}", "preLaunchTask": "maven: compile"}]}