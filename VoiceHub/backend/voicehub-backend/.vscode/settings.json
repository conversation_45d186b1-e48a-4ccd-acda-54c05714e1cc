{
    "window.commandCenter": 1,
    "editor.indentSize": 4,
    // "workbench.colorTheme": "Visual Studio Light",
    "workbench.activityBar.orientation": "vertical",
    "svn.default.encoding": "utf-8",
    // "svn.path": "/opt/homebrew/bin/svn",
    "java.configuration.maven.userSettings": "/Users/<USER>/environment/apache-maven-3.9.9/conf/settings.xml",
    "java.configuration.maven.globalSettings": "/Users/<USER>/environment/apache-maven-3.9.9/conf/settings.xml",
    "git.autofetch": true,
    "editor.fontSize": 12,
    "cursor.composer.collapsePaneInputBoxPills": true,
    "cursor.composer.renderPillsInsteadOfBlocks": true,
    "cursor.cmdk.useThemedDiffBackground": true,
    "cursor.diffs.useCharacterLevelDiffs": true,
    "cursor.terminal.usePreviewBox": true,
    "cursor.cpp.enablePartialAccepts": true,
    // Java配置
    "java.home": "/Users/<USER>/Library/Java/JavaVirtualMachines/ms-17.0.15/Contents/Home",
    "java.jdt.ls.java.home": "/Users/<USER>/Library/Java/JavaVirtualMachines/ms-17.0.15/Contents/Home",
    "java.configuration.runtimes": [
        {
            "name": "JavaSE-17",
            "path": "/Users/<USER>/Library/Java/JavaVirtualMachines/ms-17.0.15/Contents/Home",
            "default": true
        }
    ],
    "java.compile.nullAnalysis.mode": "automatic",
    "java.configuration.updateBuildConfiguration": "interactive",
    "java.saveActions.organizeImports": true,
    "java.format.settings.url": null,
    "java.format.settings.profile": null
  }