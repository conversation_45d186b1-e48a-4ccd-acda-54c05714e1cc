package com.voicehub.backend.entity;

import javax.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.hibernate.annotations.CreationTimestamp;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.UUID;

/**
 * Voice Analytics Entity
 * Stores detailed voice analysis data for quality assessment and improvement tracking
 */
@Entity
@Table(name = "voice_analytics")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class VoiceAnalytics {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;

    @Column(name = "session_id")
    private UUID sessionId;

    @Column(name = "recording_type", length = 50)
    private String recordingType; // NOTE, CONVERSATION, SCHEDULE

    @Column(name = "recording_id")
    private Long recordingId;

    @Column(name = "duration_seconds", nullable = false)
    private Integer durationSeconds;

    @Column(name = "audio_quality_score", precision = 4, scale = 2)
    private BigDecimal audioQualityScore;

    @Column(name = "speech_rate_wpm")
    private Integer speechRateWpm;

    @Column(name = "pause_frequency", precision = 5, scale = 2)
    private BigDecimal pauseFrequency;

    @Column(name = "volume_consistency", precision = 5, scale = 2)
    private BigDecimal volumeConsistency;

    @Column(name = "clarity_score", precision = 4, scale = 2)
    private BigDecimal clarityScore;

    @Column(name = "pitch_analysis", columnDefinition = "text")
    private String pitchAnalysis;

    @Column(name = "frequency_analysis", columnDefinition = "text")
    private String frequencyAnalysis;

    @Column(name = "noise_level", precision = 5, scale = 2)
    private BigDecimal noiseLevel;

    @Column(name = "voice_activity_ratio", precision = 5, scale = 4)
    private BigDecimal voiceActivityRatio;

    @Column(name = "emotion_detected", length = 50)
    private String emotionDetected;

    @Column(name = "emotion_confidence", precision = 5, scale = 4)
    private BigDecimal emotionConfidence;

    @Column(name = "language_detected", length = 10)
    private String languageDetected;

    @Column(name = "language_confidence", precision = 5, scale = 4)
    private BigDecimal languageConfidence;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    // Enums for recording types
    public enum RecordingType {
        NOTE, CONVERSATION, SCHEDULE
    }

    // Enums for emotions
    public enum EmotionType {
        HAPPY, SAD, ANGRY, SURPRISED, NEUTRAL, EXCITED, CALM, FRUSTRATED
    }

    // Helper methods
    public RecordingType getRecordingTypeEnum() {
        try {
            return RecordingType.valueOf(recordingType.toUpperCase());
        } catch (Exception e) {
            return RecordingType.NOTE;
        }
    }

    public void setRecordingTypeEnum(RecordingType recordingType) {
        this.recordingType = recordingType.name();
    }

    public EmotionType getEmotionDetectedEnum() {
        try {
            return EmotionType.valueOf(emotionDetected.toUpperCase());
        } catch (Exception e) {
            return EmotionType.NEUTRAL;
        }
    }

    public void setEmotionDetectedEnum(EmotionType emotion) {
        this.emotionDetected = emotion.name();
    }

    // Calculated properties
    public boolean isHighQuality() {
        return audioQualityScore != null && audioQualityScore.compareTo(new BigDecimal("8.0")) >= 0;
    }

    public boolean isOptimalSpeechRate() {
        return speechRateWpm != null && speechRateWpm >= 120 && speechRateWpm <= 160;
    }

    public boolean isGoodVoiceActivity() {
        return voiceActivityRatio != null && voiceActivityRatio.compareTo(new BigDecimal("0.7")) >= 0;
    }

    public String getQualityLevel() {
        if (audioQualityScore == null) return "UNKNOWN";
        
        if (audioQualityScore.compareTo(new BigDecimal("9.0")) >= 0) return "EXCELLENT";
        if (audioQualityScore.compareTo(new BigDecimal("8.0")) >= 0) return "GOOD";
        if (audioQualityScore.compareTo(new BigDecimal("6.0")) >= 0) return "FAIR";
        return "POOR";
    }

    @PrePersist
    protected void onCreate() {
        if (sessionId == null) {
            sessionId = UUID.randomUUID();
        }
    }
}