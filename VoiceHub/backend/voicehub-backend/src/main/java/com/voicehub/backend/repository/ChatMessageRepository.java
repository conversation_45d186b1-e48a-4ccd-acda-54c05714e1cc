package com.voicehub.backend.repository;

import com.voicehub.backend.entity.ChatMessage;
import com.voicehub.backend.entity.Conversation;
import com.voicehub.backend.entity.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Chat Message Repository
 * Data access layer for chat message management
 */
@Repository
public interface ChatMessageRepository extends JpaRepository<ChatMessage, Long> {

    /**
     * Find messages by conversation
     */
    List<ChatMessage> findByConversationOrderByCreatedAtAsc(Conversation conversation);

    /**
     * Find messages by conversation with pagination
     */
    Page<ChatMessage> findByConversationOrderByCreatedAtAsc(Conversation conversation, Pageable pageable);

    /**
     * Find recent messages in conversation
     */
    @Query("SELECT m FROM ChatMessage m WHERE m.conversation = :conversation ORDER BY m.createdAt DESC")
    List<ChatMessage> findRecentMessages(@Param("conversation") Conversation conversation, Pageable pageable);

    /**
     * Find messages by role
     */
    List<ChatMessage> findByConversationAndRoleOrderByCreatedAtAsc(Conversation conversation, ChatMessage.MessageRole role);

    /**
     * Find voice messages
     */
    List<ChatMessage> findByConversationAndIsVoiceInputTrueOrderByCreatedAtAsc(Conversation conversation);

    /**
     * Search messages by content
     */
    @Query("SELECT m FROM ChatMessage m WHERE m.conversation = :conversation AND " +
           "LOWER(m.content) LIKE LOWER(CONCAT('%', :keyword, '%')) ORDER BY m.createdAt ASC")
    List<ChatMessage> searchByContent(@Param("conversation") Conversation conversation, @Param("keyword") String keyword);

    /**
     * Find messages with specific emotion
     */
    List<ChatMessage> findByConversationAndEmotionDetectedOrderByCreatedAtAsc(Conversation conversation, String emotion);

    /**
     * Find messages with high emotion confidence
     */
    @Query("SELECT m FROM ChatMessage m WHERE m.conversation = :conversation AND m.emotionConfidence >= :minConfidence ORDER BY m.emotionConfidence DESC")
    List<ChatMessage> findHighConfidenceEmotions(@Param("conversation") Conversation conversation, @Param("minConfidence") Double minConfidence);

    /**
     * Count messages by conversation
     */
    long countByConversation(Conversation conversation);

    /**
     * Count messages by role
     */
    long countByConversationAndRole(Conversation conversation, ChatMessage.MessageRole role);

    /**
     * Count voice messages
     */
    long countByConversationAndIsVoiceInputTrue(Conversation conversation);

    /**
     * Get total token count for conversation
     */
    @Query("SELECT COALESCE(SUM(m.tokenCount), 0) FROM ChatMessage m WHERE m.conversation = :conversation")
    Long getTotalTokenCount(@Param("conversation") Conversation conversation);

    /**
     * Get average response time for conversation
     */
    @Query("SELECT AVG(m.responseTimeMs) FROM ChatMessage m WHERE m.conversation = :conversation AND m.role = 'ASSISTANT' AND m.responseTimeMs IS NOT NULL")
    Double getAverageResponseTime(@Param("conversation") Conversation conversation);

    /**
     * Find messages created today
     */
    @Query("SELECT m FROM ChatMessage m WHERE m.conversation = :conversation AND DATE(m.createdAt) = CURRENT_DATE ORDER BY m.createdAt ASC")
    List<ChatMessage> findTodayMessages(@Param("conversation") Conversation conversation);

    /**
     * Find messages in date range
     */
    @Query("SELECT m FROM ChatMessage m WHERE m.conversation = :conversation AND m.createdAt BETWEEN :startDate AND :endDate ORDER BY m.createdAt ASC")
    List<ChatMessage> findMessagesByDateRange(@Param("conversation") Conversation conversation, 
                                            @Param("startDate") LocalDateTime startDate, 
                                            @Param("endDate") LocalDateTime endDate);

    /**
     * Find user messages across all conversations
     */
    @Query("SELECT m FROM ChatMessage m JOIN m.conversation c WHERE c.user = :user AND m.role = 'USER' ORDER BY m.createdAt DESC")
    List<ChatMessage> findUserMessages(@Param("user") User user, Pageable pageable);

    /**
     * Search user messages across all conversations
     */
    @Query("SELECT m FROM ChatMessage m JOIN m.conversation c WHERE c.user = :user AND " +
           "LOWER(m.content) LIKE LOWER(CONCAT('%', :keyword, '%')) ORDER BY m.createdAt DESC")
    List<ChatMessage> searchUserMessages(@Param("user") User user, @Param("keyword") String keyword);

    /**
     * Find messages with specific emotion across user's conversations
     */
    @Query("SELECT m FROM ChatMessage m JOIN m.conversation c WHERE c.user = :user AND m.emotionDetected = :emotion ORDER BY m.createdAt DESC")
    List<ChatMessage> findMessagesByEmotion(@Param("user") User user, @Param("emotion") String emotion);

    /**
     * Get emotion statistics for user
     */
    @Query("SELECT m.emotionDetected, COUNT(m) as count, AVG(m.emotionConfidence) as avgConfidence " +
           "FROM ChatMessage m JOIN m.conversation c WHERE c.user = :user AND m.emotionDetected IS NOT NULL " +
           "GROUP BY m.emotionDetected ORDER BY count DESC")
    List<Object[]> getEmotionStatistics(@Param("user") User user);

    /**
     * Find longest messages
     */
    @Query("SELECT m FROM ChatMessage m WHERE m.conversation = :conversation ORDER BY LENGTH(m.content) DESC")
    List<ChatMessage> findLongestMessages(@Param("conversation") Conversation conversation, Pageable pageable);

    /**
     * Delete old messages
     */
    @Query("DELETE FROM ChatMessage m WHERE m.conversation = :conversation AND m.createdAt < :cutoffDate")
    void deleteOldMessages(@Param("conversation") Conversation conversation, @Param("cutoffDate") LocalDateTime cutoffDate);

    /**
     * Find messages with voice files
     */
    @Query("SELECT m FROM ChatMessage m WHERE m.conversation = :conversation AND m.voiceFilePath IS NOT NULL ORDER BY m.createdAt ASC")
    List<ChatMessage> findMessagesWithVoiceFiles(@Param("conversation") Conversation conversation);

    /**
     * Get daily message count for conversation
     */
    @Query("SELECT DATE(m.createdAt) as date, COUNT(m) as count FROM ChatMessage m WHERE m.conversation = :conversation " +
           "AND m.createdAt >= :since GROUP BY DATE(m.createdAt) ORDER BY date DESC")
    List<Object[]> getDailyMessageCount(@Param("conversation") Conversation conversation, @Param("since") LocalDateTime since);


}