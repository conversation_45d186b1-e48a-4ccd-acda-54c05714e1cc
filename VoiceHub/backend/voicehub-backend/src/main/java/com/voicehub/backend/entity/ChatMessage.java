package com.voicehub.backend.entity;

import javax.persistence.*;
import org.hibernate.annotations.CreationTimestamp;

import java.time.LocalDateTime;

/**
 * Chat Message Entity
 * Represents individual messages in a conversation
 */
@Entity
@Table(name = "chat_messages")
public class ChatMessage {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(columnDefinition = "TEXT", nullable = false)
    private String content;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private MessageType type;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private MessageRole role;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "conversation_id", nullable = false)
    private Conversation conversation;

    @Column(name = "emotion_detected")
    private String emotionDetected;

    @Column(name = "emotion_confidence")
    private Double emotionConfidence;

    @Column(name = "response_time_ms")
    private Long responseTimeMs;

    @Column(name = "token_count")
    private Integer tokenCount;

    @Column(name = "is_voice_input")
    private Boolean isVoiceInput = false;

    @Column(name = "voice_file_path")
    private String voiceFilePath;

    @Column(name = "transcription_confidence")
    private Double transcriptionConfidence;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    // Enums
    public enum MessageType {
        TEXT,
        VOICE,
        SYSTEM,
        ERROR
    }

    public enum MessageRole {
        USER,
        ASSISTANT,
        SYSTEM
    }

    // Constructors
    public ChatMessage() {}

    public ChatMessage(String content, MessageType type, MessageRole role, Conversation conversation) {
        this.content = content;
        this.type = type;
        this.role = role;
        this.conversation = conversation;
    }

    // Static factory methods
    public static ChatMessage createUserMessage(String content, Conversation conversation) {
        return new ChatMessage(content, MessageType.TEXT, MessageRole.USER, conversation);
    }

    public static ChatMessage createAssistantMessage(String content, Conversation conversation) {
        return new ChatMessage(content, MessageType.TEXT, MessageRole.ASSISTANT, conversation);
    }

    public static ChatMessage createVoiceMessage(String content, String voiceFilePath, Conversation conversation) {
        ChatMessage message = new ChatMessage(content, MessageType.VOICE, MessageRole.USER, conversation);
        message.setIsVoiceInput(true);
        message.setVoiceFilePath(voiceFilePath);
        return message;
    }

    public static ChatMessage createSystemMessage(String content, Conversation conversation) {
        return new ChatMessage(content, MessageType.SYSTEM, MessageRole.SYSTEM, conversation);
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public MessageType getType() {
        return type;
    }

    public void setType(MessageType type) {
        this.type = type;
    }

    public MessageRole getRole() {
        return role;
    }

    public void setRole(MessageRole role) {
        this.role = role;
    }

    public Conversation getConversation() {
        return conversation;
    }

    public void setConversation(Conversation conversation) {
        this.conversation = conversation;
    }

    public String getEmotionDetected() {
        return emotionDetected;
    }

    public void setEmotionDetected(String emotionDetected) {
        this.emotionDetected = emotionDetected;
    }

    public Double getEmotionConfidence() {
        return emotionConfidence;
    }

    public void setEmotionConfidence(Double emotionConfidence) {
        this.emotionConfidence = emotionConfidence;
    }

    public Long getResponseTimeMs() {
        return responseTimeMs;
    }

    public void setResponseTimeMs(Long responseTimeMs) {
        this.responseTimeMs = responseTimeMs;
    }

    public Integer getTokenCount() {
        return tokenCount;
    }

    public void setTokenCount(Integer tokenCount) {
        this.tokenCount = tokenCount;
    }

    public Boolean getIsVoiceInput() {
        return isVoiceInput;
    }

    public void setIsVoiceInput(Boolean isVoiceInput) {
        this.isVoiceInput = isVoiceInput;
    }

    public String getVoiceFilePath() {
        return voiceFilePath;
    }

    public void setVoiceFilePath(String voiceFilePath) {
        this.voiceFilePath = voiceFilePath;
    }

    public Double getTranscriptionConfidence() {
        return transcriptionConfidence;
    }

    public void setTranscriptionConfidence(Double transcriptionConfidence) {
        this.transcriptionConfidence = transcriptionConfidence;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    // Utility methods
    public boolean isFromUser() {
        return MessageRole.USER.equals(this.role);
    }

    public boolean isFromAssistant() {
        return MessageRole.ASSISTANT.equals(this.role);
    }

    public boolean isVoiceMessage() {
        return MessageType.VOICE.equals(this.type) || Boolean.TRUE.equals(this.isVoiceInput);
    }

    public void setEmotionAnalysis(String emotion, Double confidence) {
        this.emotionDetected = emotion;
        this.emotionConfidence = confidence;
    }

    @Override
    public String toString() {
        return "ChatMessage{" +
                "id=" + id +
                ", role=" + role +
                ", type=" + type +
                ", content='" + (content != null ? content.substring(0, Math.min(50, content.length())) : "") + "..." + '\'' +
                ", createdAt=" + createdAt +
                '}';
    }
}