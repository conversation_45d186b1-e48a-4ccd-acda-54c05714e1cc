package com.voicehub.backend.entity;

import javax.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.hibernate.annotations.CreationTimestamp;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * Mood Entry Entity
 * Tracks user emotional states and mood patterns over time
 */
@Entity
@Table(name = "mood_entries")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class MoodEntry {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;

    @Enumerated(EnumType.STRING)
    @Column(name = "mood", nullable = false, length = 50)
    private MoodType mood;

    @Column(name = "intensity", nullable = false)
    private Integer intensity; // 1-10 scale

    @Column(name = "note", columnDefinition = "TEXT")
    private String note;

    @ElementCollection
    @CollectionTable(name = "mood_entry_triggers", joinColumns = @JoinColumn(name = "mood_entry_id"))
    @Column(name = "trigger")
    private List<String> triggers;

    @Column(name = "context", length = 100)
    private String context; // VOICE_SESSION, MANUAL, CONVERSATION

    @Column(name = "related_session_id")
    private UUID relatedSessionId;

    @Column(name = "location", length = 100)
    private String location;

    @Column(name = "weather", length = 50)
    private String weather;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    // Enums
    public enum MoodType {
        HAPPY, SAD, ANGRY, SURPRISED, NEUTRAL, EXCITED, CALM, FRUSTRATED, ANXIOUS, CONTENT
    }

    public enum ContextType {
        VOICE_SESSION, MANUAL, CONVERSATION, SCHEDULE_REMINDER, SYSTEM_PROMPT
    }

    // Helper methods
    public ContextType getContextEnum() {
        try {
            return ContextType.valueOf(context.toUpperCase());
        } catch (Exception e) {
            return ContextType.MANUAL;
        }
    }

    public void setContextEnum(ContextType contextType) {
        this.context = contextType.name();
    }

    public boolean isPositiveMood() {
        return mood == MoodType.HAPPY || mood == MoodType.EXCITED || mood == MoodType.CONTENT || mood == MoodType.CALM;
    }

    public boolean isNegativeMood() {
        return mood == MoodType.SAD || mood == MoodType.ANGRY || mood == MoodType.FRUSTRATED || mood == MoodType.ANXIOUS;
    }

    public boolean isHighIntensity() {
        return intensity != null && intensity >= 8;
    }

    public boolean isLowIntensity() {
        return intensity != null && intensity <= 3;
    }

    public String getIntensityLevel() {
        if (intensity == null) return "UNKNOWN";
        
        if (intensity >= 8) return "HIGH";
        if (intensity >= 6) return "MEDIUM";
        if (intensity >= 4) return "LOW";
        return "VERY_LOW";
    }

    public String getMoodCategory() {
        if (isPositiveMood()) return "POSITIVE";
        if (isNegativeMood()) return "NEGATIVE";
        return "NEUTRAL";
    }

    // Validation
    @PrePersist
    @PreUpdate
    protected void validate() {
        if (intensity != null && (intensity < 1 || intensity > 10)) {
            throw new IllegalArgumentException("Intensity must be between 1 and 10");
        }
    }
}