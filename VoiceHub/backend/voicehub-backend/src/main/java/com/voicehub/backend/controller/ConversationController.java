package com.voicehub.backend.controller;

import com.voicehub.backend.entity.ChatMessage;
import com.voicehub.backend.entity.Conversation;
import com.voicehub.backend.entity.User;
import com.voicehub.backend.service.ConversationService;
import com.voicehub.backend.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * Conversation Controller
 * REST API endpoints for conversation and chat management
 */
@RestController
@RequestMapping("/api/conversations")
@Tag(name = "Conversations", description = "Conversation and chat management APIs")
@PreAuthorize("hasRole('USER')")
public class ConversationController {

    private static final Logger logger = LoggerFactory.getLogger(ConversationController.class);

    @Autowired
    private ConversationService conversationService;

    @Autowired
    private UserService userService;

    /**
     * Create new conversation
     */
    @PostMapping
    @Operation(summary = "Create Conversation", description = "Create a new conversation")
    public ResponseEntity<?> createConversation(
            @RequestBody Map<String, Object> request,
            Authentication authentication) {
        
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            
            String title = (String) request.get("title");
            String typeStr = (String) request.getOrDefault("type", "GENERAL");
            Conversation.ConversationType type = Conversation.ConversationType.valueOf(typeStr.toUpperCase());

            Conversation conversation = conversationService.createConversation(title, type, user);

            logger.info("Successfully created conversation: {} for user: {}", conversation.getId(), user.getUsername());
            return ResponseEntity.ok(createSuccessResponse("Conversation created successfully", conversation));

        } catch (Exception e) {
            logger.error("Failed to create conversation: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Failed to create conversation: " + e.getMessage()));
        }
    }

    /**
     * Send message to conversation
     */
    @PostMapping("/{id}/messages")
    @Operation(summary = "Send Message", description = "Send a message to conversation and get AI response")
    public ResponseEntity<?> sendMessage(
            @PathVariable Long id,
            @RequestBody Map<String, Object> request,
            Authentication authentication) {
        
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            
            String content = (String) request.get("content");
            Boolean isVoice = (Boolean) request.getOrDefault("isVoice", false);
            String voiceFilePath = (String) request.get("voiceFilePath");

            if (content == null || content.trim().isEmpty()) {
                return ResponseEntity.badRequest().body(createErrorResponse("Message content is required"));
            }

            ChatMessage response;
            if (Boolean.TRUE.equals(isVoice) && voiceFilePath != null) {
                response = conversationService.sendVoiceMessage(id, content, voiceFilePath, user);
            } else {
                response = conversationService.sendMessage(id, content, user);
            }

            logger.info("Successfully sent message to conversation: {} for user: {}", id, user.getUsername());
            return ResponseEntity.ok(createSuccessResponse("Message sent successfully", response));

        } catch (Exception e) {
            logger.error("Failed to send message: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Failed to send message: " + e.getMessage()));
        }
    }

    /**
     * Get all conversations for user
     */
    @GetMapping
    @Operation(summary = "Get Conversations", description = "Get user's conversations with pagination")
    public ResponseEntity<?> getConversations(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            Authentication authentication) {
        
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            Pageable pageable = PageRequest.of(page, size);
            Page<Conversation> conversations = conversationService.getUserConversations(user, pageable);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", conversations.getContent());
            response.put("totalElements", conversations.getTotalElements());
            response.put("totalPages", conversations.getTotalPages());
            response.put("currentPage", page);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("Failed to get conversations: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Failed to get conversations: " + e.getMessage()));
        }
    }

    /**
     * Get conversation by ID
     */
    @GetMapping("/{id}")
    @Operation(summary = "Get Conversation", description = "Get conversation by ID")
    public ResponseEntity<?> getConversation(
            @PathVariable Long id,
            Authentication authentication) {
        
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            Optional<Conversation> conversation = conversationService.getConversation(id, user);

            if (conversation.isPresent()) {
                return ResponseEntity.ok(createSuccessResponse("Conversation retrieved successfully", conversation.get()));
            } else {
                return ResponseEntity.notFound().build();
            }

        } catch (Exception e) {
            logger.error("Failed to get conversation: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Failed to get conversation: " + e.getMessage()));
        }
    }

    /**
     * Get conversation messages
     */
    @GetMapping("/{id}/messages")
    @Operation(summary = "Get Messages", description = "Get messages in conversation")
    public ResponseEntity<?> getMessages(
            @PathVariable Long id,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "50") int size,
            Authentication authentication) {
        
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            
            if (page == 0 && size == 50) {
                // Return all messages for default request
                List<ChatMessage> messages = conversationService.getConversationMessages(id, user);
                return ResponseEntity.ok(createSuccessResponse("Messages retrieved successfully", messages));
            } else {
                // Return paginated messages
                Pageable pageable = PageRequest.of(page, size);
                Page<ChatMessage> messages = conversationService.getConversationMessages(id, user, pageable);
                
                Map<String, Object> response = new HashMap<>();
                response.put("success", true);
                response.put("data", messages.getContent());
                response.put("totalElements", messages.getTotalElements());
                response.put("totalPages", messages.getTotalPages());
                response.put("currentPage", page);

                return ResponseEntity.ok(response);
            }

        } catch (Exception e) {
            logger.error("Failed to get messages: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Failed to get messages: " + e.getMessage()));
        }
    }

    /**
     * Update conversation
     */
    @PutMapping("/{id}")
    @Operation(summary = "Update Conversation", description = "Update conversation details")
    public ResponseEntity<?> updateConversation(
            @PathVariable Long id,
            @RequestBody Map<String, Object> request,
            Authentication authentication) {
        
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            
            String title = (String) request.get("title");
            String typeStr = (String) request.get("type");
            Conversation.ConversationType type = typeStr != null ? Conversation.ConversationType.valueOf(typeStr.toUpperCase()) : null;

            Conversation updatedConversation = conversationService.updateConversation(id, title, type, user);

            logger.info("Successfully updated conversation: {} for user: {}", id, user.getUsername());
            return ResponseEntity.ok(createSuccessResponse("Conversation updated successfully", updatedConversation));

        } catch (Exception e) {
            logger.error("Failed to update conversation: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Failed to update conversation: " + e.getMessage()));
        }
    }

    /**
     * Delete conversation
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "Delete Conversation", description = "Delete conversation by ID")
    public ResponseEntity<?> deleteConversation(
            @PathVariable Long id,
            Authentication authentication) {
        
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            conversationService.deleteConversation(id, user);

            logger.info("Successfully deleted conversation: {} for user: {}", id, user.getUsername());
            return ResponseEntity.ok(createSuccessResponse("Conversation deleted successfully", null));

        } catch (Exception e) {
            logger.error("Failed to delete conversation: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Failed to delete conversation: " + e.getMessage()));
        }
    }

    /**
     * Search conversations
     */
    @GetMapping("/search")
    @Operation(summary = "Search Conversations", description = "Search conversations by keyword")
    public ResponseEntity<?> searchConversations(
            @RequestParam String keyword,
            Authentication authentication) {
        
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            List<Conversation> conversations = conversationService.searchConversations(user, keyword);

            return ResponseEntity.ok(createSuccessResponse("Search completed successfully", conversations));

        } catch (Exception e) {
            logger.error("Failed to search conversations: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Failed to search conversations: " + e.getMessage()));
        }
    }

    /**
     * Get conversations by type
     */
    @GetMapping("/type/{type}")
    @Operation(summary = "Get Conversations by Type", description = "Get conversations filtered by type")
    public ResponseEntity<?> getConversationsByType(
            @PathVariable Conversation.ConversationType type,
            Authentication authentication) {
        
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            List<Conversation> conversations = conversationService.getConversationsByType(user, type);

            return ResponseEntity.ok(createSuccessResponse("Conversations retrieved successfully", conversations));

        } catch (Exception e) {
            logger.error("Failed to get conversations by type: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Failed to get conversations by type: " + e.getMessage()));
        }
    }

    /**
     * Get favorite conversations
     */
    @GetMapping("/favorites")
    @Operation(summary = "Get Favorite Conversations", description = "Get user's favorite conversations")
    public ResponseEntity<?> getFavoriteConversations(Authentication authentication) {
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            List<Conversation> conversations = conversationService.getFavoriteConversations(user);

            return ResponseEntity.ok(createSuccessResponse("Favorite conversations retrieved successfully", conversations));

        } catch (Exception e) {
            logger.error("Failed to get favorite conversations: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Failed to get favorite conversations: " + e.getMessage()));
        }
    }

    /**
     * Get archived conversations
     */
    @GetMapping("/archived")
    @Operation(summary = "Get Archived Conversations", description = "Get user's archived conversations")
    public ResponseEntity<?> getArchivedConversations(Authentication authentication) {
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            List<Conversation> conversations = conversationService.getArchivedConversations(user);

            return ResponseEntity.ok(createSuccessResponse("Archived conversations retrieved successfully", conversations));

        } catch (Exception e) {
            logger.error("Failed to get archived conversations: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Failed to get archived conversations: " + e.getMessage()));
        }
    }

    /**
     * Get recent conversations
     */
    @GetMapping("/recent")
    @Operation(summary = "Get Recent Conversations", description = "Get recent conversations")
    public ResponseEntity<?> getRecentConversations(
            @RequestParam(defaultValue = "7") int days,
            Authentication authentication) {
        
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            List<Conversation> conversations = conversationService.getRecentConversations(user, days);

            return ResponseEntity.ok(createSuccessResponse("Recent conversations retrieved successfully", conversations));

        } catch (Exception e) {
            logger.error("Failed to get recent conversations: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Failed to get recent conversations: " + e.getMessage()));
        }
    }

    /**
     * Toggle favorite status
     */
    @PutMapping("/{id}/favorite")
    @Operation(summary = "Toggle Favorite", description = "Toggle favorite status of conversation")
    public ResponseEntity<?> toggleFavorite(
            @PathVariable Long id,
            Authentication authentication) {
        
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            Conversation conversation = conversationService.toggleFavorite(id, user);

            logger.info("Successfully toggled favorite for conversation: {} for user: {}", id, user.getUsername());
            return ResponseEntity.ok(createSuccessResponse("Favorite status updated successfully", conversation));

        } catch (Exception e) {
            logger.error("Failed to toggle favorite: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Failed to toggle favorite: " + e.getMessage()));
        }
    }

    /**
     * Toggle archive status
     */
    @PutMapping("/{id}/archive")
    @Operation(summary = "Toggle Archive", description = "Toggle archive status of conversation")
    public ResponseEntity<?> toggleArchive(
            @PathVariable Long id,
            Authentication authentication) {
        
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            Conversation conversation = conversationService.toggleArchive(id, user);

            logger.info("Successfully toggled archive for conversation: {} for user: {}", id, user.getUsername());
            return ResponseEntity.ok(createSuccessResponse("Archive status updated successfully", conversation));

        } catch (Exception e) {
            logger.error("Failed to toggle archive: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Failed to toggle archive: " + e.getMessage()));
        }
    }

    /**
     * Update conversation status
     */
    @PutMapping("/{id}/status")
    @Operation(summary = "Update Status", description = "Update conversation status")
    public ResponseEntity<?> updateStatus(
            @PathVariable Long id,
            @RequestBody Map<String, Object> request,
            Authentication authentication) {
        
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            String statusStr = (String) request.get("status");
            Conversation.ConversationStatus status = Conversation.ConversationStatus.valueOf(statusStr.toUpperCase());

            Conversation conversation = conversationService.updateConversationStatus(id, status, user);

            logger.info("Successfully updated status for conversation: {} for user: {}", id, user.getUsername());
            return ResponseEntity.ok(createSuccessResponse("Status updated successfully", conversation));

        } catch (Exception e) {
            logger.error("Failed to update status: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Failed to update status: " + e.getMessage()));
        }
    }

    /**
     * Generate conversation summary
     */
    @PostMapping("/{id}/summary")
    @Operation(summary = "Generate Summary", description = "Generate AI summary for conversation")
    public ResponseEntity<?> generateSummary(
            @PathVariable Long id,
            Authentication authentication) {
        
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            Conversation conversation = conversationService.generateSummary(id, user);

            logger.info("Successfully generated summary for conversation: {} for user: {}", id, user.getUsername());
            return ResponseEntity.ok(createSuccessResponse("Summary generated successfully", conversation));

        } catch (Exception e) {
            logger.error("Failed to generate summary: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Failed to generate summary: " + e.getMessage()));
        }
    }

    /**
     * Get conversation statistics
     */
    @GetMapping("/stats")
    @Operation(summary = "Get Statistics", description = "Get user's conversation statistics")
    public ResponseEntity<?> getConversationStats(Authentication authentication) {
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            ConversationService.ConversationStats stats = conversationService.getUserConversationStats(user);

            return ResponseEntity.ok(createSuccessResponse("Statistics retrieved successfully", stats));

        } catch (Exception e) {
            logger.error("Failed to get conversation stats: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Failed to get statistics: " + e.getMessage()));
        }
    }

    /**
     * Search messages across conversations
     */
    @GetMapping("/messages/search")
    @Operation(summary = "Search Messages", description = "Search messages across all conversations")
    public ResponseEntity<?> searchMessages(
            @RequestParam String keyword,
            Authentication authentication) {
        
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            List<ChatMessage> messages = conversationService.searchMessages(user, keyword);

            return ResponseEntity.ok(createSuccessResponse("Message search completed successfully", messages));

        } catch (Exception e) {
            logger.error("Failed to search messages: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Failed to search messages: " + e.getMessage()));
        }
    }

    /**
     * Get messages by emotion
     */
    @GetMapping("/messages/emotion/{emotion}")
    @Operation(summary = "Get Messages by Emotion", description = "Get messages filtered by emotion")
    public ResponseEntity<?> getMessagesByEmotion(
            @PathVariable String emotion,
            Authentication authentication) {
        
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            List<ChatMessage> messages = conversationService.getMessagesByEmotion(user, emotion);

            return ResponseEntity.ok(createSuccessResponse("Messages retrieved successfully", messages));

        } catch (Exception e) {
            logger.error("Failed to get messages by emotion: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Failed to get messages by emotion: " + e.getMessage()));
        }
    }

    /**
     * Get emotion statistics
     */
    @GetMapping("/emotions/stats")
    @Operation(summary = "Get Emotion Statistics", description = "Get user's emotion statistics")
    public ResponseEntity<?> getEmotionStats(Authentication authentication) {
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            List<Object[]> stats = conversationService.getEmotionStatistics(user);

            return ResponseEntity.ok(createSuccessResponse("Emotion statistics retrieved successfully", stats));

        } catch (Exception e) {
            logger.error("Failed to get emotion stats: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Failed to get emotion statistics: " + e.getMessage()));
        }
    }

    /**
     * Create success response
     */
    private Map<String, Object> createSuccessResponse(String message, Object data) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", message);
        response.put("data", data);
        return response;
    }

    /**
     * Create error response
     */
    private Map<String, Object> createErrorResponse(String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("error", message);
        return response;
    }
}